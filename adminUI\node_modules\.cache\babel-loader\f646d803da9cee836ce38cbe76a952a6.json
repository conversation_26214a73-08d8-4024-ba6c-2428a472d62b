{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue", "mtime": 1754377844971}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"UserCenter\",\n  data: function data() {\n    return {\n      userFrom: {\n        keywords: \"\",\n        phone: \"\",\n        level: [],\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      tableData: [],\n      levelList: [],\n      levelData: [],\n      loading: false\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getLevelList();\n  },\n  methods: {\n    handlelevelFilter: function handlelevelFilter(status) {\n      if (!String(status) && status !== 0) {\n        return \"\";\n      }\n      var array = this.levelList.filter(function (item) {\n        return status === item.grade;\n      });\n      if (array.length) {\n        return array[0].name;\n      } else {\n        return \"\";\n      }\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this = this;\n      this.loading = true;\n      this.userFrom.page = num ? num : this.userFrom.page;\n      this.userFrom.level = this.levelData.join(\",\");\n      if (this.loginType == 0) this.userFrom.userType = \"\";\n      (0, _user.userListApi)(this.userFrom).then(function (res) {\n        _this.tableData = res.list;\n        _this.userFrom.total = res.total;\n        _this.loading = false;\n      }).catch(function () {\n        _this.loading = false;\n      });\n      this.checkedCities = this.$cache.local.has(\"user_stroge\") ? this.$cache.local.getJSON(\"user_stroge\") : this.checkedCities;\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.userFrom.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.userFrom.limit = index;\n      this.getList();\n    },\n    resetForm: function resetForm() {\n      this.userFrom = {\n        keywords: \"\",\n        phone: \"\",\n        level: [],\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.getList();\n    },\n    // 获取等级列表\n    getLevelList: function getLevelList() {\n      var _this2 = this;\n      (0, _user.levelListApi)().then(function (res) {\n        _this2.levelList = res;\n        // 存储到 localStorage 供 levelFilter 使用\n        localStorage.setItem('levelKey', JSON.stringify(res));\n        // 等级数据加载完成后再加载用户列表\n        _this2.getList();\n      });\n    },\n    // 获取升级方式文本\n    getUpgradeTypeText: function getUpgradeTypeText(type) {\n      return this.$t(\"user.grade.upgradeTypes.\".concat(type)) || this.$t('common.unknown');\n    }\n  }\n};", null]}