{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue?vue&type=template&id=43544c9c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue", "mtime": 1754377844971}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div class=\"container mt-1\">\n      <el-form v-model=\"userFrom\" inline size=\"small\">\n        <el-form-item :label=\"$t('user.center.nickname') + '：'\">\n          <el-input\n            v-model=\"userFrom.keywords\"\n            :placeholder=\"$t('user.center.nickname')\"\n            clearable\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('user.center.phone') + '：'\">\n          <el-input\n            v-model=\"userFrom.phone\"\n            :placeholder=\"$t('user.center.phone')\"\n            clearable\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('user.center.userLevel') + '：'\">\n          <el-select\n            v-model=\"levelData\"\n            :placeholder=\"$t('common.pleaseSelect')\"\n            class=\"selWidth\"\n            clearable\n            filterable\n            multiple\n          >\n            <el-option\n              :value=\"item.id\"\n              v-for=\"(item, index) in levelList\"\n              :key=\"index\"\n              :label=\"item.name\"\n            >\n              <span style=\"float: left\">{{ item.name }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">\n                {{ getUpgradeTypeText(item.upgradeType) }}\n                <span v-if=\"item.upgradeType === 1\"> - Rp {{ item.upgradePrice }}</span>\n              </span>\n            </el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"getList(1)\">\n      {{ $t(\"common.query\") }}\n    </el-button>\n\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"resetForm()\">\n      {{ $t(\"common.reset\") }}\n    </el-button>\n  </el-card>\n\n  <el-card class=\"box-card\" style=\"margin-top: 12px;\">\n    <el-table\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        :label=\"$t('common.serialNumber')\"\n        type=\"index\"\n        width=\"110\"\n      ></el-table-column>\n      <el-table-column :label=\"$t('user.center.avatar')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.nickname')\"\n        min-width=\"150\"\n        prop=\"nickname\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.nickname | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <!-- <el-table-column :label=\"$t('user.center.tiktokAccount')\" min-width=\"80\"></el-table-column> -->\n      <el-table-column :label=\"$t('user.center.tiktokId')\" min-width=\"150\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.openId | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('user.center.phone')\" min-width=\"100\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.phone | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('user.center.whatsApp')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.whatsAppAccount | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.registerTime')\"\n        min-width=\"150\"\n        prop=\"createTime\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.lastLoginTime')\"\n        min-width=\"150\"\n        prop=\"lastLoginTime\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.orderCount')\"\n        min-width=\"80\"\n        prop=\"payCount\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.orderFinishCount')\"\n        min-width=\"80\"\n        prop=\"spreadCount\"\n      ></el-table-column>\n      <el-table-column :label=\"$t('user.center.isAgent')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.isAgent | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('user.center.isPartner')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.isPartner | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.userLevelLabel')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{\n            handlelevelFilter(scope.row.level) | filterEmpty\n          }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.inviter')\"\n        min-width=\"80\"\n        prop=\"spreadNickname\"\n      ></el-table-column>\n    </el-table>\n    <el-pagination\n      class=\"mt20\"\n      @size-change=\"sizeChange\"\n      @current-change=\"pageChange\"\n      :current-page=\"userFrom.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"userFrom.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"userFrom.total\"\n    >\n    </el-pagination>\n  </el-card>\n</div>\n", null]}