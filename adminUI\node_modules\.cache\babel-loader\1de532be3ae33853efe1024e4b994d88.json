{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\lang\\zh-CN.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\lang\\zh-CN.js", "mtime": 1754377406953}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _product, _request, _history, _admin, _affiliateProducts, _common$navbar$common;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar _default = exports.default = (_common$navbar$common = {\n  common: {\n    confirm: \"确定\",\n    cancel: \"取消\",\n    tip: \"提示\",\n    cancelled: \"已取消\",\n    deleteFile: \"永久删除该文件\",\n    systemTip: \"系统提示\"\n  },\n  navbar: {\n    home: \"主页\",\n    profile: \"个人中心\",\n    logout: \"退出\"\n  }\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common$navbar$common, \"common\", _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n  editSuccess: \"修改成功\",\n  addSuccess: \"新增成功\",\n  confirmDelete: \"是否确认删除名称为“{name}”的数据项？\",\n  status: \"状态\",\n  fetchDataFailed: \"获取数据失败\",\n  operationSuccess: \"操作成功\",\n  operationFailed: \"操作失败\",\n  unknownError: \"未知错误\",\n  confirm: \"确定\",\n  cancel: \"取消\",\n  deleteConfirm: \"确定删除？\",\n  deleteSuccess: \"删除成功\",\n  deleteFailed: \"删除失败\",\n  saveSuccess: \"保存成功\",\n  saveFailed: \"保存失败\",\n  enterRejectReason: \"请输入拒绝原因\",\n  startDate: \"开始日期\",\n  endDate: \"结束日期\",\n  all: \"全部\",\n  serialNumber: \"序号\",\n  query: \"查询\",\n  reset: \"重置\",\n  enter: \"请输入\",\n  pendingReview: \"待审核\",\n  reviewedPassed: \"已通过\",\n  reviewedRejected: \"已拒绝\",\n  pleaseSelect: \"请选择\",\n  yes: \"是\",\n  no: \"否\",\n  show: \"显示\",\n  hide: \"不显示\",\n  unknown: \"未知\",\n  keyword: {\n    text: \"文字消息\",\n    image: \"图片消息\",\n    news: \"图文消息\",\n    voice: \"声音消息\"\n  },\n  couponType: {\n    general: \"通用券\",\n    product: \"商品券\",\n    category: \"品类券\"\n  },\n  couponReceive: {\n    manual: \"手动领取\",\n    newUser: \"新人券\",\n    gift: \"赠送券\"\n  },\n  paymentStatus: {\n    unpaid: \"未支付\",\n    paid: \"已支付\"\n  },\n  withdrawType: {\n    bank: \"银行卡\",\n    alipay: \"支付宝\",\n    wechat: \"微信\"\n  },\n  rechargeType: {\n    wechatPublic: \"微信公众号\",\n    wechatH5: \"微信H5支付\",\n    miniProgram: \"小程序\"\n  },\n  withdrawStatus: {\n    rejected: \"已拒绝\",\n    reviewing: \"审核中\",\n    withdrawn: \"已提现\"\n  }\n}, \"status\", {\n  bargain: {\n    1: \"进行中\",\n    2: \"未完成\",\n    3: \"已成功\"\n  }\n}), \"onePass\", {\n  sms: \"短信\",\n  copy: \"商品采集\",\n  expr_query: \"物流查询\",\n  expr_dump: \"电子面单打印\"\n}), \"editStatus\", {\n  1: \"未审核\",\n  2: \"审核中\",\n  3: \"审核失败\",\n  4: \"审核成功\"\n}), \"videoStatus\", {\n  0: \"初始值\",\n  5: \"上架\",\n  11: \"自主下架\",\n  13: \"违规下架/风控系统下架\"\n}), \"actions\", \"操作\")), \"appMain\", {\n  copyright: \"Copyright © 2025\"\n}), \"dashboard\", {\n  home: \"首页\",\n  brandCenter: \"品牌中心\",\n  brandManage: \"品牌管理\",\n  productManage: \"商品管理\",\n  appManage: \"App管理\",\n  homeManage: \"首页管理\",\n  opsCenter: \"运营中心\",\n  affiliateProducts: \"联盟选品\",\n  withdrawalReview: \"提现审核\",\n  withdrawalRecords: \"提现记录\",\n  orderCenter: \"订单中心\",\n  orderInquiry: \"订单查询\",\n  userCenter: \"用户中心\",\n  userManage: \"用户管理\",\n  userLevel: \"用户等级\",\n  levelUpgradeOrder: \"等级升级订单\",\n  financeCenter: \"财务中心\",\n  financeDetails: \"财务明细\",\n  withdrawalRequest: \"提现申请\",\n  paramSettings: \"参数设置\",\n  rewardRules: \"奖励规则设置\",\n  referralRewardConfig: \"拉新奖励配置\",\n  withdrawalFee: \"提现手续费设置\",\n  membershipFee: \"会员升级费设置\",\n  accountCenter: \"账户中心\",\n  adminPermissions: \"管理权限\",\n  roleManage: \"角色管理\",\n  adminList: \"管理员列表\",\n  permissionRules: \"权限规则\",\n  profile: \"个人中心\",\n  systemSettings: \"系统设置\",\n  chainTransferRecord: \"转链记录\",\n  platformCashbackRate: \"平台返现率设置\",\n  shoppingCashbackRules: \"购物返佣规则\"\n}), \"platformCashbackRate\", {\n  platformCashbackRate: \"平台返现率\",\n  editTitle: \"编辑平台返现率\",\n  addTitle: \"新增平台返现率\",\n  placeholder: {\n    platformCashbackRate: \"请输入平台返现率\"\n  }\n}), \"tagsView\", {\n  refresh: \"刷新\",\n  close: \"关闭\",\n  closeOthers: \"关闭其他\",\n  closeAll: \"关闭所有\"\n}), \"homepage\", {\n  welcome: \"欢迎来到GENCO管理后台！\",\n  paymentSwitch: \"支付开关\",\n  paymentSwitchTip1: \"支付开关开启则前端允许设计为代理和合作伙伴，\",\n  paymentSwitchTip2: \"开关关闭则不允许升级。此处只为方便AppStore提交审核，\",\n  paymentSwitchTip3: \"请勿随便操作。\",\n  loginMode: \"登录方式\",\n  loginModeTip1: \"登录方式仅用于控制登录页TikTok登录和短信登录的\",\n  loginModeTip2: \"显示和隐藏，方便AppStore提交审核，请勿随便操作。\",\n  tikTokLogin: \"TikTok登录\",\n  smsLogin: \"短信登录\",\n  submit: \"确定\"\n}), \"brand\", {\n  search: \"品牌搜索：\",\n  status: \"状态：\",\n  pleaseSelect: \"请选择\",\n  reset: \"重置\",\n  query: \"查询\",\n  addBrand: \"新增品牌\",\n  batchOnline: \"批量上架\",\n  batchOffline: \"批量下架\",\n  batchDelete: \"批量删除\",\n  brandLogo: \"品牌Logo\",\n  brandName: \"品牌名称\",\n  industry: \"所属行业\",\n  platform: \"入驻电商平台\",\n  productCount: \"商品数量\",\n  maxCashback: \"商品最高返现率\",\n  soldCount: \"商品已售数量\",\n  soldAmount: \"商品已售总金额（Rp）\",\n  cashbackAmount: \"商品已返现金额（Rp）\",\n  shareCount: \"品牌分享数量\",\n  createTime: \"品牌创建时间\",\n  creator: \"创建人员\",\n  statusLabel: \"状态\",\n  isHot: \"是否是热卖品牌\",\n  isHighCashback: \"是否是高返现品牌\",\n  offline: \"下架\",\n  online: \"上架\",\n  edit: \"编辑\",\n  delete: \"删除\",\n  addDialogTitle: \"新增品牌\",\n  editDialogTitle: \"编辑品牌\",\n  brandNameInput: \"请输入品牌名称\",\n  brandLogoInput: \"请输入图片地址\",\n  contactPerson: \"联系人：\",\n  contactPhone: \"联系电话：\",\n  confirm: \"确定\",\n  update: \"更新\",\n  cancel: \"取消\",\n  platformTiktok: \"TikTok\",\n  platformShopee: \"Shopee\",\n  confirmOperation: \"是否进行此操作？\",\n  prompt: \"提示\",\n  productList: \"商品\",\n  isOnline: \"已上架\",\n  isOutline: \"待上架\",\n  isOuted: \"已下架\",\n  selectTip: \"请选择要上架下架删除的内容\",\n  refreshBrands: \"刷新品牌数据\",\n  refreshingBrands: \"正在刷新品牌数据...\"\n}), \"product\", (_product = {\n  search: \"商品搜索：\",\n  keywordsPlaceholder: \"请输入商品名称、关键字\",\n  status: \"状态：\",\n  pleaseSelect: \"请选择\",\n  query: \"查询\",\n  reset: \"重置\",\n  addProduct: \"新增商品\",\n  batchOnline: \"批量上架\",\n  batchOffline: \"批量下架\",\n  batchDelete: \"批量删除\",\n  productImage: \"商品图\",\n  productName: \"商品名称\",\n  productPrice: \"商品价格(Rp)\",\n  cashbackRate: \"商品返现率\",\n  estimatedCashback: \"预计返现金额（Rp）\",\n  productLink: \"商品链接\",\n  shareCount: \"商品分享数量\",\n  soldCount: \"已售数量\",\n  cashbackAmount: \"已返现金额（Rp）\",\n  addTime: \"商品添加时间\",\n  action: \"操作\",\n  offline: \"下架\",\n  online: \"上架\",\n  edit: \"编辑\",\n  delete: \"删除\",\n  isHot: \"热门\",\n  isBenefit: \"高返现\",\n  isTikTok: \"TikTok\",\n  addDialogTitle: \"新增商品\",\n  editDialogTitle: \"编辑商品\",\n  enterProductLink: \"请输入商品链接\",\n  fetchProductInfo: \"拉取商品信息\",\n  enterProductName: \"请输入商品名称\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_product, \"productPrice\", \"商品价格\"), \"enterProductPrice\", \"请输入商品价格\"), \"usercashbackRate\", \"用户返现率\"), \"enterCashbackRate\", \"请输入返现率\"), \"enterCashbackAmount\", \"请输入返现金额\"), \"isOnline\", \"是否上架：\"), \"yes\", \"是\"), \"no\", \"否\"), \"confirm\", \"确定\"), \"cancel\", \"取消\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_product, \"all\", \"全部\"), \"fetchProductFailed\", \"拉取商品信息失败\"), \"isOnIndex\", \"是否显示在首页\"), \"isOnline\", \"已上架\"), \"isOutline\", \"待上架\"), \"isOuted\", \"已下架\"))), \"operations\", {\n  withdrawal: {\n    walletWithdrawal: \"电子钱包提现\",\n    bankWithdrawal: \"银行卡提现\",\n    applicant: \"申请人\",\n    applicationTime: \"申请时间\",\n    electronicWallet: \"电子钱包\",\n    bankName: \"银行名称\",\n    applicationId: \"申请ID\",\n    applicantName: \"申请人\",\n    withdrawalAmount: \"提现金额\",\n    serviceFee: \"手续费\",\n    actualAmount: \"实际到账金额\",\n    walletCode: \"电子钱包\",\n    walletAccount: \"账户\",\n    bankCardNumber: \"银行卡号\",\n    name: \"姓名\",\n    phoneNumber: \"手机号\",\n    withdrawalCount: \"历史提现次数\",\n    auditResult: \"审核结果\",\n    rejectReason: \"拒绝原因\",\n    approve: \"审核通过\",\n    reject: \"审核拒绝\",\n    rejectReview: \"审核拒绝\",\n    exportExcel: \"导出excel表\",\n    transferTime: \"转账时间\",\n    transferResult: \"转账结果\",\n    remark: \"备注\",\n    attachment: \"附件\",\n    operator: \"操作人\",\n    withdrawalStatus: \"提现状态\",\n    ShopeePay: \"ShopeePay\",\n    DANA: \"DANA\",\n    OVO: \"OVO\",\n    Gopay: \"Gopay\",\n    unapproved: \"未通过\",\n    underReview: \"审核中\",\n    reviewed: \"已审核\",\n    paid: \"已打款\"\n  }\n}), \"order\", {\n  search: {\n    orderNo: \"订单编号\",\n    productTitle: \"商品名称\",\n    status: \"状态\",\n    all: \"全部\",\n    query: \"查询\",\n    reset: \"重置\",\n    exportExcel: \"导出excel表\",\n    serialNumber: \"序号\",\n    productImage: \"商品图片\",\n    orderId: \"订单ID\",\n    productName: \"商品名称\",\n    payCount: \"购买数量\",\n    actualCommission: \"商品价格（Rp）\",\n    payPrice: \"订单金额（Rp）\",\n    commissionRate: \"商品返现率\",\n    estimatedCommission: \"预计返现金额（Rp）\",\n    contentId: \"电商平台\",\n    statusLabel: \"订单状态\",\n    unknown: \"未知\",\n    ordered: \"已下单\",\n    settled: \"已结算\",\n    refunded: \"已退款\",\n    frozen: \"冻结\",\n    deducted: \"已扣除\",\n    totalPrice: \"金额\",\n    userCashBackRate: \"用户返现率\",\n    creatTime: \"下单时间\"\n  }\n}), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common$navbar$common, \"user\", {\n  center: {\n    nickname: \"昵称\",\n    phone: \"手机号\",\n    userLevel: \"用户等级\",\n    query: \"查询\",\n    reset: \"重置\",\n    serialNumber: \"序号\",\n    avatar: \"头像\",\n    tiktokAccount: \"TikTok昵称\",\n    tiktokId: \"TikTok ID\",\n    whatsApp: \"WhatsApp号\",\n    registerTime: \"注册时间\",\n    lastLoginTime: \"上次登陆时间\",\n    orderCount: \"用户下单数量\",\n    orderFinishCount: \"订单完成数量\",\n    isAgent: \"是否是代理\",\n    isPartner: \"是否是合作伙伴\",\n    userLevelLabel: \"用户等级\",\n    inviter: \"邀请人\",\n    userTags: \"用户标签\"\n  },\n  levelUpgrade: {\n    title: \"等级升级订单管理\",\n    orderNo: \"订单号\",\n    userId: \"用户ID\",\n    upgradeInfo: \"升级信息\",\n    upgradeFee: \"升级费用\",\n    paymentMethod: \"支付方式\",\n    orderStatus: \"订单状态\",\n    createTime: \"创建时间\",\n    payTime: \"支付时间\",\n    operation: \"操作\",\n    enterOrderNo: \"请输入订单号\",\n    selectStatus: \"请选择状态\",\n    pending: \"待支付\",\n    paid: \"已支付\",\n    cancelled: \"已取消\",\n    refunded: \"已退款\",\n    cancelOrder: \"取消订单\",\n    viewDetail: \"查看详情\",\n    orderDetail: \"订单详情\",\n    fromLevel: \"原等级\",\n    toLevel: \"目标等级\",\n    remark: \"备注\",\n    noRemark: \"无\",\n    unpaid: \"未支付\",\n    confirmCancel: \"确定要取消这个订单吗？\",\n    cancelSuccess: \"订单已取消\",\n    cancelFailed: \"取消订单失败\",\n    getListFailed: \"获取订单列表失败\",\n    balancePayment: \"余额支付\",\n    unknownLevel: \"未知等级\",\n    unknownStatus: \"未知状态\",\n    changeWarning: \"请勿频繁更改，以免计算产生混乱！\",\n    deductExperience: \"扣除经验\",\n    levelNames: {\n      1: \"普通用户\",\n      2: \"银牌用户\",\n      3: \"金牌用户\",\n      4: \"钻石用户\",\n      5: \"王者用户\",\n      6: \"总团用户\"\n    }\n  },\n  grade: {\n    title: \"用户等级\",\n    levelName: \"等级名称\",\n    experience: \"经验\",\n    discount: \"享受折扣\",\n    commissionRate: \"佣金比例\",\n    upgradeType: \"升级方式\",\n    upgradeFee: \"升级费用\",\n    availableStatus: \"开放状态\",\n    status: \"状态\",\n    operation: \"操作\",\n    available: \"已开放\",\n    unavailable: \"未开放\",\n    free: \"免费\",\n    addUserLevel: \"添加用户等级\",\n    levelIcon: \"等级图标\",\n    enable: \"开启\",\n    disable: \"关闭\",\n    edit: \"编辑\",\n    delete: \"删除\",\n    deleteConfirm: \"删除吗？删除会导致对应用户等级数据清空，请谨慎操作！\",\n    deleteSuccess: \"删除成功\",\n    updateSuccess: \"修改成功\",\n    hideConfirm: \"该操作会导致对应用户等级隐藏，请谨慎操作\",\n    userTypes: {\n      wechat: \"微信用户\",\n      routine: \"小程序用户\",\n      h5: \"H5用户\"\n    },\n    upgradeTypes: {\n      0: \"注册即可\",\n      1: \"付费购买\",\n      2: \"线下申请\",\n      3: \"渠道合作\"\n    },\n    form: {\n      dialogTitle: \"用户等级\",\n      levelNameLabel: \"等级名称\",\n      levelNamePlaceholder: \"请输入等级名称\",\n      gradeLabel: \"等级\",\n      gradePlaceholder: \"请输入等级\",\n      discountLabel: \"享受折扣(%)\",\n      discountPlaceholder: \"请输入享受折扣\",\n      experienceLabel: \"经验\",\n      experiencePlaceholder: \"请输入经验\",\n      iconLabel: \"图标\",\n      cancel: \"取 消\",\n      confirm: \"确 定\",\n      editSuccess: \"编辑成功\",\n      addSuccess: \"添加成功\",\n      validation: {\n        levelNameRequired: \"请输入等级名称\",\n        gradeRequired: \"请输入等级\",\n        gradeNumber: \"等级必须为数字值\",\n        discountRequired: \"请输入折扣\",\n        experienceRequired: \"请输入经验\",\n        experienceNumber: \"经验必须为数字值\",\n        iconRequired: \"请上传图标\",\n        imageRequired: \"请上传用户背景\"\n      }\n    }\n  }\n}), \"financial\", {\n  detail: _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    title: \"财务明细\",\n    purchaseDetail: \"会员购买明细\",\n    tradeDetail: \"交易明细\",\n    rechargeType: \"产品名称\",\n    transactionTime: \"交易时间\",\n    paymentMethod: \"付款方式\",\n    electronicWallet: \"电子钱包\",\n    bankName: \"付款银行\",\n    serialNumber: \"序号\",\n    paymentTime: \"交易时间\",\n    paymentNo: \"付款流水号\",\n    actualPaymentAmount: \"实际付款金额\",\n    institutionNumber: \"机构编号\",\n    paymentAccount: \"付款账号\",\n    mobile: \"手机号\",\n    payee: \"收款人\",\n    payeeAccount: \"收款账号\",\n    tradeNo: \"交易流水号\",\n    tradeType: \"交易类型\",\n    tradeAmount: \"交易金额（Rp）\",\n    userNickname: \"用户昵称\",\n    tikTokAccount: \"TickTok\",\n    whatsApp: \"WhatsApp号\",\n    channel: \"渠道\",\n    orderNo: \"订单编号\",\n    bankTransfer: \"银行转账\"\n  }, \"electronicWallet\", \"电子钱包\"), \"agentFee\", \"代理费\"), \"partnerFee\", \"合作伙伴费\"), \"exportExcel\", \"导出excel表\"), \"ShopeePay\", \"ShopeePay\"), \"DANA\", \"DANA\"), \"OVO\", \"OVO\"), \"Gopay\", \"Gopay\"),\n  request: (_request = {\n    walletWithdrawal: \"电子钱包提现\",\n    bankWithdrawal: \"银行卡提现\",\n    applicant: \"申请人\",\n    applicationTime: \"申请时间\",\n    electronicWallet: \"电子钱包\",\n    bankName: \"银行名称\",\n    serialNumber: \"序号\",\n    applicationId: \"申请ID\",\n    applicantName: \"申请人\",\n    withdrawalAmount: \"提现金额\",\n    serviceFee: \"手续费\",\n    actualAmount: \"实际到账金额\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_request, \"applicationTime\", \"申请时间\"), \"walletCode\", \"电子钱包\"), \"walletAccount\", \"账户\"), \"bankCardNumber\", \"银行卡号\"), \"name\", \"姓名\"), \"phoneNumber\", \"手机号\"), \"action\", \"操作\"), \"transferComplete\", \"转账完成\"), \"attachment\", \"附件\"), \"remark\", \"备注\"), _defineProperty(_defineProperty(_defineProperty(_request, \"confirm\", \"确定\"), \"cancel\", \"取消\"), \"exportExcel\", \"导出excel表\")),\n  history: (_history = {\n    walletWithdrawal: \"电子钱包提现\",\n    bankWithdrawal: \"银行卡提现\",\n    applicant: \"申请人\",\n    applicationTime: \"申请时间\",\n    electronicWallet: \"电子钱包\",\n    bankName: \"银行名称\",\n    serialNumber: \"序号\",\n    applicationId: \"申请ID\",\n    applicantName: \"申请人\",\n    withdrawalAmount: \"提现金额\",\n    serviceFee: \"手续费\",\n    actualAmount: \"实际到账金额\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_history, \"applicationTime\", \"申请时间\"), \"walletCode\", \"电子钱包\"), \"walletAccount\", \"账户\"), \"bankCardNumber\", \"银行卡号\"), \"name\", \"姓名\"), \"phoneNumber\", \"手机号\"), \"transferTime\", \"转账时间\"), \"transferResult\", \"转账结果\"), \"remark\", \"备注\"), \"attachment\", \"附件\"), _defineProperty(_defineProperty(_defineProperty(_history, \"operator\", \"操作人\"), \"exportExcel\", \"导出excel表\"), \"status\", \"提现状态\"))\n}), \"parameter\", {\n  rewardRules: {\n    title: \"奖励规则设置\",\n    rewardTemplateName: \"模板名称\",\n    rewardTemplateId: \"奖励规则模板ID\",\n    directInviteReward: \"直接邀请每人奖励\",\n    secondLevelInviteReward: \"间接邀请（二级）每人可获得（Rp）\",\n    thirdLevelInviteReward: \"间接邀请（三级）每人可获得（Rp）\",\n    goldRewardPer10: \"每发展10名金牌奖励（Rp）\",\n    diamondRewardPer10: \"每发展10名钻石奖励（Rp）\",\n    operation: \"操作\",\n    edit: \"编辑\",\n    editTitle: \"编辑奖励规则设置\",\n    directAgentLabel: \"直接邀请人是代理\",\n    directPartnerLabel: \"直接邀请人是合作伙伴\",\n    indirectAgent2LevelLabel: \"间接邀请（二级是代理）每人可获得（Rp）\",\n    indirectPartner2LevelLabel: \"间接邀请（二级是合作伙伴）每人可获得（Rp）\",\n    indirectAgent3LevelLabel: \"间接邀请（三级是代理）每人可获得（Rp）\",\n    indirectPartner3LevelLabel: \"间接邀请（三级是合作伙伴）每人可获得（Rp）\"\n  },\n  withdrawalFee: {\n    title: \"提现手续费设置\",\n    feeTemplateId: \"费率模板ID\",\n    minWithdrawAmount: \"最低提现金额（Rp）\",\n    maxWithdrawAmount: \"最高提现金额（Rp）\",\n    withdrawFeeRate: \"手续费费率（%）\",\n    operation: \"操作\",\n    edit: \"编辑\",\n    addTitle: \"新增费率规则\",\n    editTitle: \"编辑费率规则\",\n    placeholder: {\n      couponId: \"请输入代金券ID\",\n      minWithdrawAmount: \"请输入最低提现金额\",\n      maxWithdrawAmount: \"请输入最高提现金额\",\n      withdrawFeeRate: \"请输入手续费费率\"\n    }\n  },\n  membershipFee: {\n    title: \"会员升级费设置\",\n    feeTemplateId: \"费率模板ID\",\n    agentFee: \"代理费（Rp）\",\n    partnerFee: \"合作伙伴费（Rp）\",\n    operation: \"操作\",\n    edit: \"编辑\",\n    addTitle: \"新增会员升级费\",\n    editTitle: \"编辑会员升级费\",\n    placeholder: {\n      agentFee: \"请输入代理费\",\n      partnerFee: \"请输入合作伙伴费\"\n    }\n  },\n  shoppingCashbackRules: {\n    directCashbackRate: \"直接可获得返佣比例（%）\",\n    secondLevelCashbackRate: \"二级返佣比例（%）\",\n    thirdLevelCashbackRate: \"三级返佣比例（%）\",\n    normalUserRule: \"普通用户返佣规则\",\n    agentTeamRule: \"代理团队返佣规则\",\n    partnerTeamRule: \"合作伙伴团队返佣规则\"\n  },\n  referralRewardConfig: {\n    title: \"拉新奖励配置\",\n    rewardTemplateId: \"奖励规则模版ID\",\n    rewardTemplateName: \"模版名称\",\n    referralCount: \"拉新数\",\n    firstOrderCount: \"首单数\",\n    rewardAmount: \"奖励金（Rp）\",\n    rewardRuleZh: \"奖励规则（中文）\",\n    rewardRuleEn: \"奖励规则（英文）\",\n    rewardRuleId: \"奖励规则（印尼）\",\n    operation: \"操作\",\n    edit: \"编辑\",\n    editTitle: \"编辑拉新奖励配置\",\n    basicConfig: \"基础配置\",\n    validation: {\n      referralCountMin: \"拉新数必须大于等于0\",\n      firstOrderCountMin: \"首单数必须大于等于0\",\n      firstOrderCountMax: \"首单数必须小于拉新数\",\n      rewardAmountMin: \"奖励金必须大于等于0\"\n    }\n  }\n}), \"admin\", {\n  system: {\n    role: {\n      roleName: \"角色昵称\",\n      roleId: \"角色编号\",\n      status: \"状态\",\n      createTime: \"创建时间\",\n      updateTime: \"更新时间\",\n      operation: \"操作\",\n      addRole: \"新增角色\",\n      editRole: \"编辑角色\",\n      deleteRole: \"删除角色\",\n      confirmDelete: \"确认删除当前数据\",\n      deleteSuccess: \"删除数据成功\",\n      createIdentity: \"创建身份\",\n      editIdentity: \"编辑身份\",\n      roleForm: {\n        roleNameLabel: \"角色名称\",\n        roleNamePlaceholder: \"身份名称\",\n        statusLabel: \"状态\",\n        menuPermissions: \"菜单权限\",\n        expandCollapse: \"展开/折叠\",\n        selectAll: \"全选/全不选\",\n        parentChildLink: \"父子联动\",\n        confirm: \"确定\",\n        update: \"更新\",\n        cancel: \"取消\"\n      }\n    },\n    admin: (_admin = {\n      role: \"身份\",\n      status: \"状态\",\n      realName: \"姓名或账号\",\n      id: \"ID\",\n      account: \"账号\",\n      phone: \"手机号\",\n      lastTime: \"最后登录时间\",\n      lastIp: \"最后登录IP\",\n      isSms: \"是否接收短信\",\n      isDel: \"删除标记\",\n      operation: \"操作\",\n      addAdmin: \"添加管理员\",\n      edit: \"编辑\",\n      delete: \"删除\",\n      createIdentity: \"创建身份\",\n      editIdentity: \"编辑身份\",\n      pleaseAddPhone: \"请先为管理员添加手机号!\",\n      confirmDelete: \"确认删除当前数据\",\n      deleteSuccess: \"删除数据成功\"\n    }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_admin, \"account\", \"账号\"), \"pwd\", \"密码\"), \"repwd\", \"确认密码\"), \"realName\", \"姓名\"), \"roles\", \"角色\"), \"phone\", \"手机号\"), \"pleaseAddPhone\", \"请先为管理员添加手机号!\"), \"validatePhone\", {\n      required: \"请填写手机号\",\n      formatError: \"手机号格式不正确!\"\n    }), \"validatePass\", {\n      required: \"请再次输入密码\",\n      notMatch: \"两次输入密码不一致!\"\n    }), \"message\", {\n      createSuccess: \"创建管理员成功\",\n      updateSuccess: \"更新管理员成功\"\n    }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_admin, \"validateAccount\", {\n      required: \"请填写管理员账号\"\n    }), \"validatePassword\", {\n      required: \"请填写管理员密码\"\n    }), \"validateConfirmPassword\", {\n      required: \"请确认密码\"\n    }), \"validateRealName\", {\n      required: \"请填写管理员姓名\"\n    }), \"validateRoles\", {\n      required: \"请选择管理员角色\"\n    }), \"validatePassword\", {\n      required: \"请填写管理员密码\",\n      lengthError: \"密码长度需为6-20个字符\"\n    }), \"validateConfirmPassword\", {\n      required: \"请确认密码\"\n    }), \"validatePass\", {\n      notMatch: \"两次输入密码不一致\"\n    }))\n  }\n}), \"permissionRules\", {\n  menuName: \"菜单名称\",\n  status: \"状态\",\n  select: \"请选择\",\n  add: \"新增\",\n  expandCollapse: \"展开/折叠\",\n  actions: {\n    edit: \"修改\",\n    add: \"新增\",\n    delete: \"删除\"\n  },\n  table: {\n    menuName: \"菜单名称\",\n    icon: \"图标\",\n    sort: \"排序\",\n    perm: \"权限标识\",\n    component: \"组件路径\",\n    status: \"状态\",\n    createTime: \"创建时间\",\n    type: \"类型\"\n  },\n  menuType: {\n    directory: \"目录\",\n    menu: \"菜单\",\n    button: \"按钮\"\n  },\n  form: {\n    parentMenu: \"上级菜单\",\n    menuType: \"菜单类型\",\n    menuIcon: \"菜单图标\",\n    menuName: \"菜单名称\",\n    sort: \"显示排序\",\n    component: \"组件路径\",\n    componentTip: \"访问的组件路径，如：`system/user/index`，默认在`views`目录下\",\n    perm: \"权限字符\",\n    permTip: '控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi(\"system: user: list\")`)',\n    showStatus: \"显示状态\",\n    showStatusTip: \"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\",\n    enterMenuName: \"请输入菜单名称\",\n    enterComponent: \"请输入组件路径\",\n    enterPerm: \"请输入权限标识\",\n    selectIcon: \"请选择菜单图标\",\n    selectParentMenu: \"选择上级菜单\",\n    sortRequired: \"显示排序不能为空\"\n  }\n}), \"affiliateProducts\", (_affiliateProducts = {\n  title: \"联盟选品\",\n  keywords: \"关键词：\",\n  keywordsPlaceholder: \"请输入商品关键词\",\n  priceRange: \"价格范围：\",\n  minPrice: \"最低价格\",\n  maxPrice: \"最高价格\",\n  commissionRange: \"佣金率范围：\",\n  minCommission: \"最低佣金率(%)\",\n  maxCommission: \"最高佣金率(%)\",\n  sort: \"排序：\",\n  sortCommissionRate: \"佣金率\",\n  sortCommission: \"佣金金额\",\n  sortPrice: \"商品价格\",\n  sortSales: \"销量\",\n  sortDesc: \"降序\",\n  sortAsc: \"升序\",\n  query: \"查询\",\n  reset: \"重置\",\n  refresh: \"刷新\",\n  listTitle: \"联盟选品列表\",\n  batchImport: \"批量入库\",\n  batchImporting: \"批量入库中...\",\n  batchDelete: \"批量删除\",\n  batchDeleting: \"批量删除中...\",\n  emptyTip: \"点击查询按钮开始搜索商品\",\n  serialNumber: \"序号\",\n  productImage: \"商品图片\",\n  productTitle: \"商品标题\",\n  shop: \"店铺\",\n  originalPrice: \"原价\",\n  salesPrice: \"售价\",\n  commissionRate: \"佣金率\",\n  commissionAmount: \"佣金金额\",\n  unitsSold: \"销量\",\n  inventoryStatus: \"库存状态\",\n  hasInventory: \"有库存\",\n  noInventory: \"无库存\",\n  saleRegion: \"销售区域\",\n  importStatus: \"导入状态\",\n  imported: \"已导入\",\n  notImported: \"未导入\",\n  action: \"操作\",\n  import: \"商品入库\",\n  importing: \"入库中...\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"imported\", \"已入库\"), \"delete\", \"删除\"), \"deleting\", \"删除中...\"), \"prevPage\", \"上一页\"), \"nextPage\", \"下一页\"), \"pageSize\", \"每页显示：\"), \"totalCount\", \"共 {count} 个商品\"), \"importSingle\", \"导入单个商品\"), \"importBatch\", \"批量导入商品\"), \"selectedCount\", \"选中商品数量：\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"brandAutoDetect\", \"品牌信息将自动从TikTok商品数据中识别，无需手动选择\"), \"confirmImport\", \"确定导入\"), \"cancel\", \"取消\"), \"deleteConfirm\", \"确定要删除这个商品吗？\"), \"batchDeleteConfirm\", \"确定要批量删除 {count} 个商品吗？\"), \"deleteSuccess\", \"删除成功\"), \"batchDeleteSuccess\", \"批量删除成功\"), \"importSuccess\", \"商品导入成功！\"), \"batchImportSuccess\", \"批量导入完成！成功导入 {count} 个商品\"), \"importExists\", \"商品已存在，无需重复导入\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"batchImportExists\", \"所有商品都已存在，无需重复导入\"), \"batchImportPartial\", \"批量导入部分成功！成功：{success}，失败：{failed}，跳过：{skipped}\"), \"batchImportMixed\", \"批量导入完成！成功：{success}，跳过（已存在）：{skipped}\"), \"importFailed\", \"商品导入失败！失败原因：{reason}\"), \"batchImportFailed\", \"批量导入失败！失败：{failed}，跳过：{skipped}\"), \"selectFirst\", \"请先选择要导入的商品\"), \"selectDeleteFirst\", \"请先选择要删除的商品\"), \"searchFirst\", \"请先点击查询按钮\"), \"noResults\", \"未找到符合条件的商品\"), \"commissionRangeError\", \"佣金率必须 ≥ 1000 或者等于 0\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"commissionInvalidNumber\", \"请输入有效的数字\"), \"pleaseFixErrors\", \"请修正输入错误后再查询\"), \"addKeywordPlaceholder\", \"输入关键词后按回车添加\"), \"keywordTooLong\", \"关键词长度不能超过255个字符\"), \"keywordDuplicate\", \"关键词已存在，请勿重复添加\"), \"keywordLimitExceeded\", \"关键词数量不能超过20个\"), \"selectedKeywords\", \"已选关键词：\"), \"clearAll\", \"清空全部\"), \"clearAllKeywords\", \"清空所有关键词\"), \"confirmClearAllKeywords\", \"确定要清空所有已选择的关键词吗？\"), _defineProperty(_affiliateProducts, \"keywordsClearedSuccess\", \"已清空所有关键词\"))), \"chainTransferRecord\", {\n  title: \"转链记录\",\n  keyword: \"关键词\",\n  brandName: \"品牌名称\",\n  query: \"查询\",\n  reset: \"重置\",\n  exportExcel: \"导出excel表\",\n  serialNumber: \"序号\",\n  nickname: \"昵称\",\n  tiktokId: \"TikTok ID\",\n  originalLink: \"原始链接\",\n  rebateLink: \"转链后的返利链接\",\n  operationTime: \"操作时间\",\n  linkSource: \"链接来源\",\n  productId: \"商品ID\",\n  productName: \"商品名称\",\n  productPrice: \"商品价格\",\n  productCashbackRate: \"商品返现率\",\n  userCashbackRate: \"用户返现率\",\n  enterProductName: \"请输入商品名称\"\n}), \"message\", {\n  hello: \"你好\",\n  userNotice: \"用户通知\",\n  userDetails: {\n    balance: \"余额\",\n    allOrderCount: \"总计订单\",\n    allConsumeCount: \"总消费金额\",\n    integralCount: \"积分\",\n    mothOrderCount: \"本月订单\",\n    mothConsumeCount: \"本月消费金额\",\n    consumeRecord: \"消费记录\",\n    integralDetail: \"积分明细\",\n    signInRecord: \"签到记录\",\n    coupons: \"持有优惠券\",\n    balanceChange: \"余额变动\",\n    friendRelation: \"好友关系\",\n    sourceOrPurpose: \"来源/用途\",\n    integralChange: \"积分变化\",\n    balanceAfterChange: \"变化后积分\",\n    date: \"日期\",\n    remark: \"备注\",\n    orderId: \"订单ID\",\n    receiver: \"收货人\",\n    goodsNum: \"商品数量\",\n    goodsTotalPrice: \"商品总价\",\n    payPrice: \"实付金额\",\n    payTime: \"交易完成时间\",\n    action: \"动作\",\n    getIntegral: \"获得积分\",\n    signTime: \"签到时间\",\n    couponName: \"优惠券名称\",\n    faceValue: \"面值\",\n    validity: \"有效期\",\n    minPrice: \"最低消费额\",\n    exchangeTime: \"兑换时间\",\n    changeAmount: \"变动金额\",\n    afterChange: \"变动后\",\n    type: \"类型\",\n    createTime: \"创建时间\",\n    id: \"ID\",\n    nickname: \"昵称\",\n    level: \"等级\",\n    joinTime: \"加入时间\"\n  }\n}));", null]}