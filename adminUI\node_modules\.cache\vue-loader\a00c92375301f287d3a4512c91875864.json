{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue?vue&type=template&id=43544c9c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue", "mtime": 1754377844971}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"container mt-1\" },\n            [\n              _c(\n                \"el-form\",\n                {\n                  attrs: { inline: \"\", size: \"small\" },\n                  model: {\n                    value: _vm.userFrom,\n                    callback: function ($$v) {\n                      _vm.userFrom = $$v\n                    },\n                    expression: \"userFrom\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: _vm.$t(\"user.center.nickname\") + \"：\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: {\n                          placeholder: _vm.$t(\"user.center.nickname\"),\n                          clearable: \"\",\n                        },\n                        model: {\n                          value: _vm.userFrom.keywords,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.userFrom, \"keywords\", $$v)\n                          },\n                          expression: \"userFrom.keywords\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: _vm.$t(\"user.center.phone\") + \"：\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: {\n                          placeholder: _vm.$t(\"user.center.phone\"),\n                          clearable: \"\",\n                        },\n                        model: {\n                          value: _vm.userFrom.phone,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.userFrom, \"phone\", $$v)\n                          },\n                          expression: \"userFrom.phone\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: { label: _vm.$t(\"user.center.userLevel\") + \"：\" },\n                    },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticClass: \"selWidth\",\n                          attrs: {\n                            placeholder: _vm.$t(\"common.pleaseSelect\"),\n                            clearable: \"\",\n                            filterable: \"\",\n                            multiple: \"\",\n                          },\n                          model: {\n                            value: _vm.levelData,\n                            callback: function ($$v) {\n                              _vm.levelData = $$v\n                            },\n                            expression: \"levelData\",\n                          },\n                        },\n                        _vm._l(_vm.levelList, function (item, index) {\n                          return _c(\n                            \"el-option\",\n                            {\n                              key: index,\n                              attrs: { value: item.id, label: item.name },\n                            },\n                            [\n                              _c(\"span\", { staticStyle: { float: \"left\" } }, [\n                                _vm._v(_vm._s(item.name)),\n                              ]),\n                              _vm._v(\" \"),\n                              _c(\n                                \"span\",\n                                {\n                                  staticStyle: {\n                                    float: \"right\",\n                                    color: \"#8492a6\",\n                                    \"font-size\": \"13px\",\n                                  },\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                \" +\n                                      _vm._s(\n                                        _vm.getUpgradeTypeText(item.upgradeType)\n                                      ) +\n                                      \"\\n                \"\n                                  ),\n                                  item.upgradeType === 1\n                                    ? _c(\"span\", [\n                                        _vm._v(\n                                          \" - Rp \" + _vm._s(item.upgradePrice)\n                                        ),\n                                      ])\n                                    : _vm._e(),\n                                ]\n                              ),\n                            ]\n                          )\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"mr10\",\n              attrs: { size: \"small\", type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.getList(1)\n                },\n              },\n            },\n            [_vm._v(\"\\n      \" + _vm._s(_vm.$t(\"common.query\")) + \"\\n    \")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"mr10\",\n              attrs: { size: \"small\", type: \"\" },\n              on: {\n                click: function ($event) {\n                  return _vm.resetForm()\n                },\n              },\n            },\n            [_vm._v(\"\\n      \" + _vm._s(_vm.$t(\"common.reset\")) + \"\\n    \")]\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\", staticStyle: { \"margin-top\": \"12px\" } },\n        [\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              attrs: {\n                data: _vm.tableData,\n                size: \"small\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"common.serialNumber\"),\n                  type: \"index\",\n                  width: \"110\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.avatar\"),\n                  \"min-width\": \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.avatar,\n                                \"preview-src-list\": [scope.row.avatar],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.nickname\"),\n                  \"min-width\": \"150\",\n                  prop: \"nickname\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.nickname))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.tiktokId\"),\n                  \"min-width\": \"150\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.openId))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.phone\"),\n                  \"min-width\": \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.phone))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.whatsApp\"),\n                  \"min-width\": \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              _vm._f(\"filterEmpty\")(scope.row.whatsAppAccount)\n                            )\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.registerTime\"),\n                  \"min-width\": \"150\",\n                  prop: \"createTime\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.lastLoginTime\"),\n                  \"min-width\": \"150\",\n                  prop: \"lastLoginTime\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.orderCount\"),\n                  \"min-width\": \"80\",\n                  prop: \"payCount\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.orderFinishCount\"),\n                  \"min-width\": \"80\",\n                  prop: \"spreadCount\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.isAgent\"),\n                  \"min-width\": \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.isAgent))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.isPartner\"),\n                  \"min-width\": \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.isPartner))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.userLevelLabel\"),\n                  \"min-width\": \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              _vm._f(\"filterEmpty\")(\n                                _vm.handlelevelFilter(scope.row.level)\n                              )\n                            )\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"user.center.inviter\"),\n                  \"min-width\": \"80\",\n                  prop: \"spreadNickname\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\"el-pagination\", {\n            staticClass: \"mt20\",\n            attrs: {\n              \"current-page\": _vm.userFrom.page,\n              \"page-sizes\": [20, 40, 60, 100],\n              \"page-size\": _vm.userFrom.limit,\n              layout: \"total, sizes, prev, pager, next, jumper\",\n              total: _vm.userFrom.total,\n            },\n            on: {\n              \"size-change\": _vm.sizeChange,\n              \"current-change\": _vm.pageChange,\n            },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}