{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\user\\center\\index.vue", "mtime": 1754377844971}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { userList<PERSON><PERSON>, levelList<PERSON><PERSON> } from \"@/api/user\";\r\nexport default {\r\n  name: \"UserCenter\",\r\n  data() {\r\n    return {\r\n      userFrom: {\r\n        keywords: \"\",\r\n        phone: \"\",\r\n        level: [],\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n      tableData: [],\r\n      levelList: [],\r\n      levelData: [],\r\n      loading: false\r\n    };\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.getLevelList();\r\n  },\r\n  methods: {\r\n    handlelevelFilter(status) {\r\n      if (!String(status) && status !== 0) {\r\n        return \"\";\r\n      }\r\n      let array = this.levelList.filter(item => status === item.grade);\r\n      if (array.length) {\r\n        return array[0].name;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    // 列表\r\n    getList(num) {\r\n      this.loading = true;\r\n      this.userFrom.page = num ? num : this.userFrom.page;\r\n      this.userFrom.level = this.levelData.join(\",\");\r\n      if (this.loginType == 0) this.userFrom.userType = \"\";\r\n      userListApi(this.userFrom)\r\n        .then(res => {\r\n          this.tableData = res.list;\r\n          this.userFrom.total = res.total;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n      this.checkedCities = this.$cache.local.has(\"user_stroge\")\r\n        ? this.$cache.local.getJSON(\"user_stroge\")\r\n        : this.checkedCities;\r\n    }, //切换页数\r\n    pageChange(index) {\r\n      this.userFrom.page = index;\r\n      this.getList();\r\n    },\r\n    //切换显示条数\r\n    sizeChange(index) {\r\n      this.userFrom.limit = index;\r\n      this.getList();\r\n    },\r\n    resetForm() {\r\n      this.userFrom = {\r\n        keywords: \"\",\r\n        phone: \"\",\r\n        level: [],\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      };\r\n      this.getList();\r\n    },\r\n    // 获取等级列表\r\n    getLevelList() {\r\n      levelListApi().then(res => {\r\n        this.levelList = res;\r\n        // 存储到 localStorage 供 levelFilter 使用\r\n        localStorage.setItem('levelKey', JSON.stringify(res));\r\n        // 等级数据加载完成后再加载用户列表\r\n        this.getList();\r\n      });\r\n    },\r\n    // 获取升级方式文本\r\n    getUpgradeTypeText(type) {\r\n      return this.$t(`user.grade.upgradeTypes.${type}`) || this.$t('common.unknown');\r\n    }\r\n  }\r\n};\r\n", null]}